import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/questionnaire/domain/models/questionnaire_model.dart';
import 'package:selfeng/features/questionnaire/presentation/providers/questionnaire_controller.dart';
import 'package:selfeng/features/questionnaire/presentation/providers/state/questionnaire_state.dart';
import 'package:selfeng/features/questionnaire/presentation/widgets/select_answer.dart';
import 'package:selfeng/features/setting/presentation/providers/setting_controller.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class QuestionnaireScreen extends ConsumerStatefulWidget {
  const QuestionnaireScreen({super.key});

  @override
  ConsumerState<QuestionnaireScreen> createState() =>
      _QuestionnaireScreenState();
}

class _QuestionnaireScreenState extends ConsumerState<QuestionnaireScreen> {
  late TextEditingController _controllerText;

  @override
  void initState() {
    super.initState();
    _controllerText = TextEditingController();
  }

  @override
  void dispose() {
    _controllerText.dispose();
    super.dispose();
  }

  void _handleNextPage(
    BuildContext context,
    AsyncValue<QuestionnaireState> viewState,
    QuestionnaireController viewModel,
  ) async {
    if (viewState.value?.questions[viewState.value!.currentQuestion].answer !=
        null) {
      await viewModel.nextPage(context, MediaQuery.of(context).size.width);
      _controllerText.clear();
      if (viewState.value!.currentQuestion == viewState.value!.questionLength) {
        customNav(
          context,
          isReplace: true,
          RouterName.questionnaireFinishScreen,
        );
      }
    }
  }

  void _handleBackPage(QuestionnaireController viewModel, double width) async {
    await viewModel.backPage(width);
    _controllerText.clear();
  }

  @override
  Widget build(BuildContext context) {
    final viewState = ref.watch(questionnaireControllerProvider);
    final viewModel = ref.watch(questionnaireControllerProvider.notifier);
    final settingState = ref.watch(settingControllerProvider);

    ref.listen(questionnaireControllerProvider.select((value) => value), ((
      previous,
      next,
    ) {
      next.maybeWhen(
        error:
            (error, _) => ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(error.toString()))),
        orElse: () {},
      );
    }));

    return switch (viewState) {
      AsyncData() => Scaffold(
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
            child:
                viewState.value.questionLength == 0
                    ? const SizedBox.shrink()
                    : Column(
                      children: [
                        LinearProgressIndicator(
                          value:
                              (viewState.value.currentQuestion) /
                              (viewState.value.questionLength),
                          backgroundColor: const Color(0xffFFDAD2),
                        ),
                        const SizedBox(height: 24),
                        Expanded(
                          child: ListView.builder(
                            controller: viewState.value.controllerScroll,
                            scrollDirection: Axis.horizontal,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: viewState.value.questions.length,
                            itemBuilder:
                                (context, index) => SizedBox(
                                  width: MediaQuery.of(context).size.width - 32,
                                  child: question(
                                    index,
                                    viewState.value.questions[index],
                                    settingState,
                                  ),
                                ),
                          ),
                        ),
                        if (viewState.value.questionLength > 0)
                          RepeatNextButton(
                            removeMargin: true,
                            onTapRepeat:
                                () => _handleBackPage(
                                  viewModel,
                                  MediaQuery.of(context).size.width,
                                ),
                            onTapNext:
                                () => _handleNextPage(
                                  context,
                                  viewState,
                                  viewModel,
                                ),
                            leftTitle: context.loc.back,
                            leftActive: viewState.value.currentQuestion != 0,
                            rightActive:
                                viewState
                                    .value
                                    .questions[viewState.value.currentQuestion]
                                    .answer !=
                                null,
                          ),
                      ],
                    ),
          ),
        ),
      ),
      AsyncError(:final error) => Center(child: Text('Error: $error')),
      AsyncLoading() => const Center(child: AppLoading()),
      _ => const Center(child: CircularProgressIndicator()),
    };
  }

  Widget question(int idx, QuestionnaireModel item, AsyncValue settingState) {
    return ListView(
      children: [
        Text(
          item.question?[settingState.value.getLanguageCode],
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 20),
        if (item.type == 1)
          ListView.builder(
            shrinkWrap: true,
            primary: false,
            itemCount: item.choices?.length,
            itemBuilder: (context, index) {
              final choice = item.choices![index];
              return SelectAnswer(
                title: choice.text?[settingState.value.getLanguageCode],
                icon: choice.imageUrl ?? '',
                groupValue: item.answer,
                value: choice.value ?? '',
                allowMultiple: item.multipleAnswer,
                onTap:
                    (val) =>
                        choice.value != 'other'
                            ? ref
                                .read(questionnaireControllerProvider.notifier)
                                .answer(idx, val)
                            : _showOtherChoiceDialog(idx),
              );
            },
          ),
        if (item.type == 2)
          VInputText(
            maxLines: 4,
            context.loc.shortDescription,
            controller: _controllerText,
            suffixText: '(30)',
            suffixStyle: Theme.of(
              context,
            ).textTheme.labelMedium?.copyWith(color: const Color(0xffB4A9A7)),
            onChanged:
                (val) => ref
                    .read(questionnaireControllerProvider.notifier)
                    .answer(idx, val),
          ),
        const SizedBox(height: 115),
      ],
    );
  }

  Future<void> _showOtherChoiceDialog(int idx) async {
    return showModalBottomSheet(
      context: context,
      isDismissible: false,
      isScrollControlled: true,
      builder:
          (context) => Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Container(
              height: 358,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
                color: Colors.white,
              ),
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
              child: Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 10),
                      Text(
                        context.loc.questionnaireOtherQ,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 24),
                      VInputText(
                        context.loc.questionnaireOtherA,
                        controller: _controllerText,
                      ),
                    ],
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: VButtonGradient(
                      title: context.loc.send,
                      onTap: () {
                        ref
                            .read(questionnaireControllerProvider.notifier)
                            .answer(idx, _controllerText.text);
                        context.pop();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
