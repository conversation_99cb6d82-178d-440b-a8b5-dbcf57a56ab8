import 'dart:io';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:external_path/external_path.dart';

class CertificateDownloadService {
  final Dio _dio = Dio();

  /// Downloads a certificate to the device's Downloads folder
  /// Returns a map with success status and message for user feedback
  Future<Map<String, dynamic>> downloadCertificate({
    required String url,
    required String fileName,
    Function(int, int)? onProgress,
  }) async {
    try {
      // Request storage permission
      final permissionResult = await _requestStoragePermission();
      if (!permissionResult['success']) {
        return permissionResult;
      }

      // Get Downloads directory path
      String? downloadsPath;
      if (Platform.isAndroid) {
        downloadsPath = await ExternalPath.getExternalStoragePublicDirectory(
          ExternalPath.DIRECTORY_DOWNLOAD,
        );
      } else if (Platform.isIOS) {
        // On iOS, use app documents directory as Downloads folder is not accessible
        final dir = await getApplicationDocumentsDirectory();
        downloadsPath = dir.path;
      }

      if (downloadsPath == null) {
        return {
          'success': false,
          'message': 'Unable to access Downloads folder',
          'path': null,
        };
      }

      final savePath = '$downloadsPath/$fileName';

      // Download the file with progress tracking
      await _dio.download(url, savePath, onReceiveProgress: onProgress);

      return {
        'success': true,
        'message':
            Platform.isAndroid
                ? 'Certificate saved to Downloads folder'
                : 'Certificate saved to Files app',
        'path': savePath,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Download failed: ${e.toString()}',
        'path': null,
      };
    }
  }

  /// Request necessary storage permissions
  Future<Map<String, dynamic>> _requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // For Android 13+ (API 33+), we need different permissions
        if (await _isAndroid13OrHigher()) {
          // Android 13+ doesn't require storage permission for Downloads folder
          return {'success': true, 'message': 'Permission granted'};
        } else {
          // For Android 12 and below
          final status = await Permission.storage.request();
          if (status.isGranted) {
            return {'success': true, 'message': 'Permission granted'};
          } else if (status.isPermanentlyDenied) {
            return {
              'success': false,
              'message':
                  'Storage permission permanently denied. Please enable it in app settings.',
            };
          } else {
            return {
              'success': false,
              'message':
                  'Storage permission is required to download certificates.',
            };
          }
        }
      } else {
        // iOS doesn't require explicit permission for app documents directory
        return {'success': true, 'message': 'Permission granted'};
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Permission request failed: ${e.toString()}',
      };
    }
  }

  /// Check if device is running Android 13 or higher
  Future<bool> _isAndroid13OrHigher() async {
    if (Platform.isAndroid) {
      // This is a simplified check - in a real app you might want to use
      // device_info_plus to get the exact Android version
      return true; // Assume modern Android for now
    }
    return false;
  }

  /// Open app settings for permission management
  Future<void> openAppSettings() async {
    await Permission.storage.request().then((status) {
      if (status.isPermanentlyDenied) {
        openAppSettings();
      }
    });
  }
}
