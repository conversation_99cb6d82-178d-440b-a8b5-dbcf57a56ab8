import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';
import 'package:selfeng/shared/theme/app_colors.dart';
import 'package:selfeng/shared/widgets/v_button_gradient.dart';
import '../providers/certificate_navigation_provider.dart';
import '../../app/services/certificate_download_service.dart';
import '../../app/services/certificate_share_service.dart';

class CertificateDetailScreen extends ConsumerStatefulWidget {
  final String level;

  const CertificateDetailScreen({super.key, required this.level});

  @override
  ConsumerState<CertificateDetailScreen> createState() =>
      _CertificateDetailScreenState();
}

class _CertificateDetailScreenState
    extends ConsumerState<CertificateDetailScreen> {
  final CertificateDownloadService _downloadService =
      CertificateDownloadService();
  final CertificateShareService _shareService = CertificateShareService();

  bool _isDownloading = false;
  bool _isSharing = false;
  Certificate? _selectedCertificate;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final certificateData = ref.read(certificateNavigationControllerProvider);
      if (certificateData != null && certificateData.certificates.isNotEmpty) {
        setState(() {
          // Find the selected certificate by ID, or use the first one as fallback
          if (certificateData.selectedCertificateId != null) {
            _selectedCertificate = certificateData.certificates.firstWhere(
              (cert) => cert.id == certificateData.selectedCertificateId,
              orElse: () => certificateData.certificates.first,
            );
          } else {
            _selectedCertificate = certificateData.certificates.first;
          }
        });
      }
    });
  }

  // Color scheme for different levels (matching existing pattern)
  Color _colorForLevel(String name) {
    final n = name.toLowerCase();
    if (n.contains('a1') || n.contains('beginner')) {
      return const Color(0xFF4F46E5); // Indigo 600
    } else if (n.contains('a2')) {
      return const Color(0xFF10B981); // Emerald 500
    } else if (n.contains('b1')) {
      return const Color(0xFF8B5CF6); // Violet 500
    } else if (n.contains('b2')) {
      return const Color(0xFF06B6D4); // Cyan 500
    } else if (n.contains('c1')) {
      return const Color(0xFFF59E0B); // Amber 500
    } else if (n.contains('c2') || n.contains('advanced')) {
      return const Color(0xFFEF4444); // Red 500
    }
    return const Color(0xFF2563EB); // Default: Blue 600
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Future<void> _downloadCertificate(String url, String fileName) async {
    setState(() => _isDownloading = true);

    try {
      final result = await _downloadService.downloadCertificate(
        url: url,
        fileName: fileName,
      );

      _showSnackBar(result['message'] as String, isError: !result['success']);
    } catch (e) {
      _showSnackBar('Download failed: $e', isError: true);
    } finally {
      setState(() => _isDownloading = false);
    }
  }

  Future<void> _shareCertificate(String url, String fileName) async {
    setState(() => _isSharing = true);

    try {
      final result = await _shareService.shareCertificate(
        url: url,
        fileName: fileName,
        text: 'Check out my ${widget.level} certificate!',
      );

      if (!result['success']) {
        _showSnackBar(result['message'] as String, isError: true);
      }
    } catch (e) {
      _showSnackBar('Share failed: $e', isError: true);
    } finally {
      setState(() => _isSharing = false);
    }
  }

  Future<void> _shareAllPages() async {
    if (_selectedCertificate == null) return;

    setState(() => _isSharing = true);

    try {
      final urls = <String>[_selectedCertificate!.certificateUrl];
      final fileNames = <String>['${widget.level}_certificate_page1.jpg'];

      if (_selectedCertificate!.certificateUrlPage2 != null) {
        urls.add(_selectedCertificate!.certificateUrlPage2!);
        fileNames.add('${widget.level}_certificate_page2.jpg');
      }

      final result = await _shareService.shareMultipleCertificates(
        urls: urls,
        fileNames: fileNames,
        text: 'Check out my ${widget.level} certificates!',
      );

      if (!result['success']) {
        _showSnackBar(result['message'] as String, isError: true);
      }
    } catch (e) {
      _showSnackBar('Share failed: $e', isError: true);
    } finally {
      setState(() => _isSharing = false);
    }
  }

  Widget _buildCertificateImage(String imageUrl, String pageLabel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Page label with icon
        Container(
          margin: const EdgeInsets.only(left: 8.0, bottom: 8.0),
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20.0),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.description, size: 16, color: AppColors.primary),
              const SizedBox(width: 6),
              Text(
                pageLabel,
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        // Certificate image
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 8.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16.0),
            child: AspectRatio(
              aspectRatio:
                  4 /
                  3, // Landscape certificate ratio (more typical for certificates)
              child: CachedNetworkImage(
                imageUrl: imageUrl,
                fit:
                    BoxFit
                        .contain, // Changed from cover to contain to show full certificate
                placeholder:
                    (context, url) => Container(
                      color: Colors.grey[50],
                      child: const Center(child: LoadingCircle()),
                    ),
                errorWidget:
                    (context, url, error) => Container(
                      color: Colors.grey[50],
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.description,
                              size: 60,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Certificate not available',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required VoidCallback onPressed,
    required bool isLoading,
    Color? backgroundColor,
  }) {
    return Expanded(
      child: Container(
        height: 56,
        margin: const EdgeInsets.symmetric(horizontal: 4.0),
        child: ElevatedButton.icon(
          onPressed: isLoading ? null : onPressed,
          icon:
              isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : Icon(icon, size: 20),
          label: Text(title),
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? AppColors.primary,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(14),
            ),
            elevation: 2,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final certificateData = ref.watch(certificateNavigationControllerProvider);

    // If no data is available, show error or navigate back
    if (certificateData == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Certificate Detail')),
        body: const Center(child: Text('No certificate data available')),
      );
    }

    final level = certificateData.level;
    final certificates = certificateData.certificates;

    if (_selectedCertificate == null && certificates.isNotEmpty) {
      // Find the selected certificate by ID, or use the first one as fallback
      if (certificateData.selectedCertificateId != null) {
        _selectedCertificate = certificates.firstWhere(
          (cert) => cert.id == certificateData.selectedCertificateId,
          orElse: () => certificates.first,
        );
      } else {
        _selectedCertificate = certificates.first;
      }
    }

    if (_selectedCertificate == null) {
      return Scaffold(
        appBar: AppBar(title: Text(level.name)),
        body: const Center(child: Text('No certificate selected')),
      );
    }

    final formattedDate = DateFormat(
      'd MMMM yyyy',
    ).format(_selectedCertificate!.dateIssued);
    final levelColor = _colorForLevel(level.name);
    final hasSecondPage = _selectedCertificate!.certificateUrlPage2 != null;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black54),
          onPressed: () => context.pop(),
        ),
        title: Text(
          '${level.name} Certificate',
          style: const TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Certificate Info Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: levelColor,
                borderRadius: BorderRadius.circular(16.0),
                boxShadow: [
                  BoxShadow(
                    color: levelColor.withValues(alpha: 0.3),
                    blurRadius: 18,
                    spreadRadius: 1,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _selectedCertificate!.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _selectedCertificate!.description,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Icon(Icons.schedule, color: Colors.white, size: 16),
                      const SizedBox(width: 6),
                      Text(
                        'Issued on $formattedDate',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Certificate Images
            Text(
              'Certificate Pages',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),

            // Page 1
            _buildCertificateImage(
              _selectedCertificate!.certificateUrl,
              'Page 1',
            ),

            const SizedBox(height: 12),

            // Action buttons for Page 1
            Row(
              children: [
                _buildActionButton(
                  title: 'Download',
                  icon: Icons.download,
                  onPressed:
                      () => _downloadCertificate(
                        _selectedCertificate!.certificateUrl,
                        '${widget.level}_certificate_page1.jpg',
                      ),
                  isLoading: _isDownloading,
                ),
                _buildActionButton(
                  title: 'Share',
                  icon: Icons.share,
                  onPressed:
                      () => _shareCertificate(
                        _selectedCertificate!.certificateUrl,
                        '${widget.level}_certificate_page1.jpg',
                      ),
                  isLoading: _isSharing,
                  backgroundColor: Colors.blue[600],
                ),
              ],
            ),

            // Page 2 (if exists)
            if (hasSecondPage) ...[
              const SizedBox(height: 24),
              _buildCertificateImage(
                _selectedCertificate!.certificateUrlPage2!,
                'Page 2',
              ),
              const SizedBox(height: 12),

              // Action buttons for Page 2
              Row(
                children: [
                  _buildActionButton(
                    title: 'Download',
                    icon: Icons.download,
                    onPressed:
                        () => _downloadCertificate(
                          _selectedCertificate!.certificateUrlPage2!,
                          '${widget.level}_certificate_page2.jpg',
                        ),
                    isLoading: _isDownloading,
                  ),
                  _buildActionButton(
                    title: 'Share',
                    icon: Icons.share,
                    onPressed:
                        () => _shareCertificate(
                          _selectedCertificate!.certificateUrlPage2!,
                          '${widget.level}_certificate_page2.jpg',
                        ),
                    isLoading: _isSharing,
                    backgroundColor: Colors.blue[600],
                  ),
                ],
              ),
            ],

            const SizedBox(height: 32),

            // Share All Button (if multiple pages)
            if (hasSecondPage)
              SizedBox(
                width: double.infinity,
                height: 56,
                child: VButtonGradient(
                  title: 'Share All Pages',
                  onTap: _shareAllPages,
                ),
              ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
